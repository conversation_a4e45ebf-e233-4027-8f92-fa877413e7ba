import '@assets/public/login/login.scss';
import { createApp, ref, onMounted, nextTick } from 'vue';
import Axios from 'axios';

(() => {
  const mountEl = document.getElementById('login');
  if (!mountEl) return;

  // Extract Twig-generated recovery route
  let initialRecoveryRoute = '';
  const resetForm = mountEl.querySelector('form') as HTMLFormElement | null;
  if (resetForm) {
    const rawInit = resetForm.getAttribute(':init') || resetForm.getAttribute('init') || '';
    const m = rawInit.match(/'([^']+)'/);
    if (m) initialRecoveryRoute = m[1];
    resetForm.removeAttribute(':init');
    resetForm.removeAttribute('init');
  }

  const app = createApp({
    setup() {
      const showResetPasswordDialog = ref(false);
      const sendPasswordRecoveryToEmail = ref('');
      const emailSent = ref(false);
      const emailSentSuccessfully = ref(false);
      const backendMessage = ref('');
      const recoveryRoute = ref(initialRecoveryRoute);

      const requestNewPassword = async (evt: Event) => {
        evt.preventDefault();

        if (!recoveryRoute.value) {
          console.error('Missing password recovery route.');
          return;
        }

        try {
          const body = new URLSearchParams({
            email: sendPasswordRecoveryToEmail.value,
          });

          const response = await Axios.post(recoveryRoute.value, body, {
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
          });

          emailSent.value = true;
          emailSentSuccessfully.value = !!response?.data?.successful;
          backendMessage.value = response?.data?.message ?? '';
        } catch (err) {
          console.error(err);
          emailSent.value = true;
          emailSentSuccessfully.value = false;
          backendMessage.value = '';
        }

        nextTick(() => {
          // fallback in case `{* backendMessage *}` does not evaluate
          const msgEl = mountEl.querySelector('.message-container .alert.alert-info') as HTMLElement | null;
          if (msgEl) msgEl.textContent = backendMessage.value || '';
        });
      };

      onMounted(() => {
        const usernameInput = document.getElementById('username') as HTMLInputElement | null;
        usernameInput?.focus();
        usernameInput?.select();
      });

      return {
        showResetPasswordDialog,
        sendPasswordRecoveryToEmail,
        emailSent,
        emailSentSuccessfully,
        backendMessage,
        recoveryRoute,
        requestNewPassword,
      };
    },
  });

  // Twig-safe delimiters
  // @ts-expect-error – not in official TS types
  app.config.compilerOptions.delimiters = ['{*', '*}'];

  app.mount('#login');
})();
